package cn.yizhoucp.product.biz.manager;

import cn.yizhoucp.ms.core.base.PageVO;
import cn.yizhoucp.ms.core.base.util.MDCUtil;
import cn.yizhoucp.ms.core.vo.coinservices.ChatBubbleProductVO;
import cn.yizhoucp.ms.core.vo.landingservices.AdminPageVO;
import cn.yizhoucp.product.dal.entity.ChatBubbleProduct;
import cn.yizhoucp.product.service.ChatBubbleProductService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 聊天气泡商品 服务实现类
 *
 * <AUTHOR>
 * @since 2024-05-15
 */
@Service
@Slf4j
public class ChatBubbleProductManager {

    @Resource
    private ChatBubbleProductService chatBubbleProductService;

    /**
     * 分页获取聊天气泡信息
     * @param appId 应用id
     * @param start 开始页
     * @param num   每页条数
     * @return PageVO<ChatBubbleProductVO>
     */
    public PageVO<ChatBubbleProductVO> pageGetProduct(Long appId, Long start, Integer num) {
        PageVO<ChatBubbleProductVO> result = new PageVO<>();
        result.setStart(start);
        result.setNum(num);
        // 条件
        LambdaQueryWrapper<ChatBubbleProduct> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(ChatBubbleProduct::getAppId, appId);
        // 分页参数
        Page<ChatBubbleProduct> page = new Page<>(start, num);
        IPage<ChatBubbleProduct> productPage = chatBubbleProductService.page(page, wrapper);
        result.setTotalCount(productPage.getTotal());
        result.setHasNext(productPage.getPages() > start);
        if (!CollectionUtils.isEmpty(productPage.getRecords())) {
            result.setList(this.convertFromDoListToVoList(productPage.getRecords()));
        }
        return result;
    }

    /**
     * 分页获取聊天气泡列表（后台接口）
     *
     * @param page 起始页
     * @param size 每页条数
     * @return AdminPageVO<ChatBubbleProductVO>
     */
    public AdminPageVO<ChatBubbleProductVO> adminPageList(Integer page, Integer size) {
        AdminPageVO<ChatBubbleProductVO> result = new AdminPageVO<>();
        page = page == null ? 1 : page;
        size = size == null ? 10 : size;
        PageVO<ChatBubbleProductVO> bubblePage = this.pageGetProduct(MDCUtil.getCurAppIdByMdc(), page.longValue(), size);
        if (null == bubblePage) {
            return result;
        }
        result.setItems(bubblePage.getList());
        result.setPageIndex(page);
        result.setPageSize(size);
        result.setHasNext(bubblePage.getHasNext());
        result.setTotalCount(bubblePage.getTotalCount());
        return result;
    }


    /**
     * 根据 key 获取聊天气泡
     *
     * @param appId     应用id
     * @param uniqueKey key
     * @return ChatBubbleProductVO
     */
    public ChatBubbleProductVO getProductByUniqueKey(Long appId, String uniqueKey) {
        ChatBubbleProduct product = chatBubbleProductService.findByUniqueKey(appId, uniqueKey);
        return this.convertFromDoToVo(product);
    }

    /**
     * DO 转 VO
     *
     * @param product DO
     * @return VO
     */
    private ChatBubbleProductVO convertFromDoToVo(ChatBubbleProduct product) {
        if (null == product) {
            return null;
        }
        ChatBubbleProductVO vo = new ChatBubbleProductVO();
        BeanUtils.copyProperties(product, vo);
        return vo;
    }

    /**
     * DO List 转 VO List
     *
     * @param productList DO List
     * @return VO List
     */
    private List<ChatBubbleProductVO> convertFromDoListToVoList(List<ChatBubbleProduct> productList) {
        if (CollectionUtils.isEmpty(productList)) {
            return new ArrayList<>();
        }
        return productList.stream().map(this::convertFromDoToVo).collect(Collectors.toList());
    }

    /**
     * 根据状态和 key 批量获取聊天气泡
     *
     * @param appId         应用id
     * @param status        状态
     * @param uniqueKeyList key 列表
     * @return List<ChatBubbleProductVO>
     */
    public List<ChatBubbleProductVO> batchGetProductByUniqueKeys(Long appId, String status, List<String> uniqueKeyList) {
        if (CollectionUtils.isEmpty(uniqueKeyList)) {
            return new ArrayList<>();
        }
        List<ChatBubbleProduct> productList = chatBubbleProductService.batchFindByStatusAndUniqueKeys(appId, status, uniqueKeyList);
        return this.convertFromDoListToVoList(productList);
    }


    /**
     * 新增聊天气泡
     * @param chatBubbleProduct 聊天气泡信息
     * @return 新增结果
     */
    public Boolean addChatBubble(ChatBubbleProduct chatBubbleProduct) {
        if (chatBubbleProduct == null) {
            throw new IllegalArgumentException("聊天气泡信息不能为空");
        }
        // 检查唯一标识是否已存在
        ChatBubbleProduct existingProduct = chatBubbleProductService.findByUniqueKey(
                chatBubbleProduct.getAppId(),
                chatBubbleProduct.getUniqueKey()
        );
        if (existingProduct != null) {
            throw new IllegalArgumentException("该唯一标识已存在");
        }
        return chatBubbleProductService.save(chatBubbleProduct);
    }

    /**
     * 编辑聊天气泡
     * @param chatBubbleProduct 聊天气泡信息
     * @return 编辑结果
     */
    public Boolean editChatBubble(ChatBubbleProduct chatBubbleProduct) {
        if (chatBubbleProduct == null || chatBubbleProduct.getId() == null) {
            throw new IllegalArgumentException("聊天气泡信息或ID不能为空");
        }
        // 检查是否存在
        ChatBubbleProduct existingProduct = chatBubbleProductService.getById(chatBubbleProduct.getId());
        if (existingProduct == null) {
            throw new IllegalArgumentException("聊天气泡不存在");
        }
        return chatBubbleProductService.updateById(chatBubbleProduct);
    }

}
