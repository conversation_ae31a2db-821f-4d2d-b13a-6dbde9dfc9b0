package cn.yizhoucp.product.service.impl;

import cn.yizhoucp.product.dal.entity.MedalProduct;
import cn.yizhoucp.product.dal.mapper.MedalProductMapper;
import cn.yizhoucp.product.service.MedalProductService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 勋章基础表
 *
 * <AUTHOR>
 * @since 2024-12-08
 */
@Service
@Slf4j
public class MedalProductServiceImpl extends ServiceImpl<MedalProductMapper, MedalProduct> implements MedalProductService {

    @Resource
    private MedalProductMapper medalProductMapper;

    @Override
    public MedalProduct findByUniqueKey(String uniqueKey) {
        return medalProductMapper.findByUniqueKey(uniqueKey);
    }

    @Override
    public List<MedalProduct> findByStatusAndType(Integer status, String type) {
        return medalProductMapper.findByStatusAndType(status, type);
    }

    @Override
    public List<MedalProduct> batchFindByUniqueKeys(List<String> uniqueKeyList) {
        LambdaQueryWrapper<MedalProduct> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(MedalProduct::getUniqueKey, uniqueKeyList);
        return this.list(queryWrapper);
    }
}
