common.error.100001=\u8BF7\u6C42\u53C2\u6570\u9519\u8BEF

user.package.error.deduct.fail=\u6263\u9664\u80CC\u5305\u5931\u8D25
user.package.error.not.enough=\u80CC\u5305\u8D44\u6E90\u4E0D\u8DB3
user.package.error.send.fail=\u7269\u54C1\u4E0B\u53D1\u80CC\u5305\u5931\u8D25
user.package.send.gift=\u53EE\uFF0C\u606D\u559C\u83B7\u5F97 {0} \u793C\u7269* {1} \u4E2A\uFF0C\u793C\u7269\u5DF2\u4E0B\u53D1\u81F3\u60A8\u7684\u80CC\u5305\u8BF7\u6CE8\u610F\u67E5\u6536
user.package.gift.expired={0} \uFF0C\u5C06\u5728 {1} \u8FC7\u671F\u54E6

product.error.not.exist=\u5546\u54C1\u4FE1\u606F\u4E0D\u5B58\u5728

# \u5EA7\u9A7E
dress.up.mount.entry.msg={0}\u5750\u7740{1}\u6765\u4E86

# \u8FDB\u573A\u7279\u6548
dress.up.ese.tips=<font color = '#FFF580'>\u6B22\u8FCE</font> <font color = '#FFFFFF'>{0}</font> <font color = '#FFF580'>\u534E\u4E3D\u5165\u573A</font>

# \u7528\u6237\u88C5\u626E
simple.user.dress.head.frame=\u5934\u50CF\u6846
simple.user.dress.special.effect=\u5165\u573A\u7279\u6548
simple.user.dress.mount=\u5750\u9A91
simple.user.dress.propose.ring=\u5B98\u5BA3\u6212\u6307
simple.user.dress.red.envelope.cover=\u7ea2\u5305\u5c01\u9762
simple.user.dress.home.cover=\u4e3b\u9875\u88c5\u626e
simple.user.dress.bubble=\u6d88\u606f\u6c14\u6ce1
simple.dress.head.frame.expire=\u5DF2\u8FC7\u671F
simple.dress.head.frame.forever=\u6C38\u4E45
simple.dress.head.frame.one.hours=\u4E0D\u8DB31\u5C0F\u65F6
splice.dress.head.frame.expire.hour=\u5269\u4F59{0}\u5C0F\u65F6
splice.dress.head.frame.expire.day=\u5269\u4F59{0}\u5929

simple.user.dress.chat.icon=\u804A\u5929\u6C14\u6CE1
simple.user.dress.information.card.background=\u8D44\u6599\u80CC\u666F